// Script interface for compatibility
export interface Script {
  message_id: string;
  code: string;
  updated_at: number;
  miniapp_id?: string;
  name?: string;
  version?: number;
}

// Synchronization events
export interface ScriptSyncEvent {
  type: 'script-updated' | 'script-deleted';
  messageId: string;
  script?: Script;
  timestamp: number;
}

// Extension communication interfaces
interface ExtensionMessage {
  type: string;
  action: string;
  data?: Record<string, unknown>;
  requestId?: string;
}

interface ExtensionResponse {
  success: boolean;
  data?: {
    code: string;
    version: number;
    updated_at: number;
  } | null;
  error?: string;
  requestId?: string;
}

interface ExtensionSyncEvent {
  type: 'script-updated' | 'script-deleted';
  miniappId: number;
  data?: {
    code: string;
    version: number;
    updated_at: number;
  };
}

class ScriptSyncManager {
  private eventListeners: Map<string, Set<(event: ScriptSyncEvent) => void>> = new Map();
  private pendingRequests = new Map<
    string,
    {
      resolve: (value: ExtensionResponse) => void;
      reject: (error: Error) => void;
      timeout: NodeJS.Timeout;
    }
  >();

  constructor() {
    // Listen for messages from extension
    if (typeof window !== 'undefined') {
      window.addEventListener('message', this.handleExtensionMessage.bind(this));
    }
  }

  private handleExtensionMessage(event: MessageEvent) {
    // Only accept messages from same origin for security
    if (event.origin !== window.location.origin) {
      return;
    }

    const message = event.data;

    // Handle sync events from extension
    if (message?.type === 'FROM_EXTENSION_TO_WEB' && message.syncEvent) {
      this.handleSyncEvent(message.syncEvent);
      return;
    }

    // Handle request responses
    if (message?.type === 'FROM_EXTENSION_TO_WEB' && message.requestId) {
      const pending = this.pendingRequests.get(message.requestId);
      if (pending) {
        clearTimeout(pending.timeout);
        this.pendingRequests.delete(message.requestId);

        if (message.success !== false) {
          pending.resolve(message);
        } else {
          pending.reject(new Error(message.error || 'Extension request failed'));
        }
      }
    }
  }

  private handleSyncEvent(event: ExtensionSyncEvent) {
    // Convert extension sync event to script sync event
    const scriptEvent: ScriptSyncEvent = {
      type: event.type,
      messageId: `miniapp-${event.miniappId}`,
      timestamp: Date.now(),
    };

    if (event.type === 'script-updated' && event.data) {
      scriptEvent.script = {
        message_id: `miniapp-${event.miniappId}`,
        code: event.data.code,
        updated_at: event.data.updated_at,
        miniapp_id: event.miniappId.toString(),
        version: event.data.version,
      };
    }

    this.notifyListeners(scriptEvent);
  }

  private async sendExtensionMessage(
    action: string,
    data?: Record<string, unknown>
  ): Promise<ExtensionResponse> {
    return new Promise((resolve, reject) => {
      const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;

      // Set up timeout
      const timeout = setTimeout(() => {
        this.pendingRequests.delete(requestId);
        reject(new Error('Extension request timeout'));
      }, 5000); // 5 second timeout

      // Store pending request
      this.pendingRequests.set(requestId, { resolve, reject, timeout });

      // Send message to extension via window.postMessage
      const message: ExtensionMessage = {
        type: 'FROM_WEB_TO_EXTENSION',
        action,
        data,
        requestId,
      };

      window.postMessage(message, '*');
    });
  }

  // Check if extension is available by sending a ping
  private async isExtensionAvailable(): Promise<boolean> {
    try {
      await this.sendExtensionMessage('ping');
      return true;
    } catch {
      return false;
    }
  }

  private notifyListeners(event: ScriptSyncEvent) {
    // Notify listeners for specific messageId
    const messageListeners = this.eventListeners.get(event.messageId);
    if (messageListeners) {
      messageListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in sync listener:', error);
        }
      });
    }

    // Notify global listeners (for backward compatibility)
    const globalListeners = this.eventListeners.get('*');
    if (globalListeners) {
      globalListeners.forEach(listener => {
        try {
          listener(event);
        } catch (error) {
          console.error('Error in sync listener:', error);
        }
      });
    }
  }

  // Save script and notify other contexts
  async saveScript(script: Script): Promise<void> {
    if (!script.miniapp_id) {
      throw new Error('miniapp_id is required for script operations');
    }

    script.updated_at = Date.now();

    try {
      // Check if extension is available first
      const isExtensionAvailable = await this.isExtensionAvailable();

      if (isExtensionAvailable) {
        const response = await this.sendExtensionMessage('save-script', {
          messageId: script.message_id,
          code: script.code,
          miniappId: parseInt(script.miniapp_id),
          name: script.name,
          version: script.version || 1,
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to save script');
        }
        // Extension will handle broadcasting sync events
      } else {
        throw new Error('Extension not available - script cannot be saved');
      }
    } catch (error) {
      console.error('Failed to save script via extension:', error);
      throw error; // Don't fallback to localStorage, let the caller handle the error
    }
  }

  // Delete script and notify other contexts
  async deleteScript(messageId: string, miniappId?: string): Promise<void> {
    try {
      // Check if extension is available first
      const isExtensionAvailable = await this.isExtensionAvailable();

      if (isExtensionAvailable && miniappId) {
        const response = await this.sendExtensionMessage('delete-script', {
          miniappId: parseInt(miniappId),
        });

        if (!response.success) {
          throw new Error(response.error || 'Failed to delete script');
        }
        // Extension will handle broadcasting sync events
      } else {
        throw new Error('Extension not available or miniappId missing - script cannot be deleted');
      }
    } catch (error) {
      console.error('Failed to delete script via extension:', error);
      throw error; // Don't fallback to localStorage, let the caller handle the error
    }
  }

  // Get script from extension storage
  async getScript(messageId: string, miniappId?: string): Promise<Script | null> {
    if (!miniappId) {
      throw new Error('miniappId is required for script operations');
    }

    try {
      // Check if extension is available first
      const isExtensionAvailable = await this.isExtensionAvailable();

      if (!isExtensionAvailable) {
        throw new Error('Extension not available');
      }

      const response = await this.sendExtensionMessage('get-script', {
        miniappId: parseInt(miniappId),
      });

      if (response.success && response.data) {
        return {
          message_id: messageId,
          code: response.data.code,
          updated_at: response.data.updated_at,
          miniapp_id: miniappId,
          version: response.data.version,
        };
      }

      return null;
    } catch (error) {
      console.error('Failed to get script via extension:', error);
      throw error;
    }
  }

  // Get script from miniapp developing field (new method)
  async getScriptFromMiniapp(miniappId: number): Promise<Script | null> {
    try {
      const response = await this.sendExtensionMessage('get-script', {
        miniappId,
      });

      if (!response.success || !response.data) {
        return null;
      }

      return {
        message_id: `miniapp-${miniappId}`,
        code: response.data.code,
        updated_at: response.data.updated_at,
        miniapp_id: miniappId.toString(),
        version: response.data.version,
      };
    } catch (error) {
      console.error('Failed to get script from miniapp:', error);
      return null;
    }
  }

  // Listen for sync events from other contexts
  addSyncListener(listener: (event: ScriptSyncEvent) => void, messageId?: string) {
    const key = messageId || '*'; // Use '*' for global listeners

    if (!this.eventListeners.has(key)) {
      this.eventListeners.set(key, new Set());
    }

    this.eventListeners.get(key)!.add(listener);

    return () => {
      const listeners = this.eventListeners.get(key);
      if (listeners) {
        listeners.delete(listener);
        // Clean up empty sets
        if (listeners.size === 0) {
          this.eventListeners.delete(key);
        }
      }
    };
  }

  // Cleanup
  destroy() {
    // Clear all pending requests
    this.pendingRequests.forEach(({ timeout, reject }) => {
      clearTimeout(timeout);
      reject(new Error('Script sync manager destroyed'));
    });
    this.pendingRequests.clear();

    // Clear listeners
    this.eventListeners.clear();

    // Remove event listener
    if (typeof window !== 'undefined') {
      window.removeEventListener('message', this.handleExtensionMessage.bind(this));
    }
  }
}

// Singleton instance
export const scriptSyncManager = new ScriptSyncManager();
